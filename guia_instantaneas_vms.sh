#!/bin/bash

# Guía completa para crear instantáneas de VMs

echo "📸 GUÍA COMPLETA: INSTANTÁNEAS DE VMs"
echo "===================================="

echo ""
echo "🔍 ACLARACIÓN IMPORTANTE"
echo "========================"
echo ""
echo "❌ VINAGRE NO PUEDE CREAR INSTANTÁNEAS"
echo "   • Vinagre es solo un CLIENTE VNC (visor remoto)"
echo "   • Es como un 'monitor' que se conecta a la VM"
echo "   • No gestiona las VMs, solo las muestra"
echo ""
echo "✅ QUIÉN SÍ PUEDE CREAR INSTANTÁNEAS:"
echo "   • GNOME Boxes (para VMs creadas en Boxes)"
echo "   • libvirt/virsh (para VMs creadas con libvirt)"
echo "   • VMware Workstation (para VMs de VMware)"
echo "   • VirtualBox (para VMs de VirtualBox)"

echo ""
echo "🎯 TUS VMs Y SUS OPCIONES"
echo "========================="

echo ""
echo "📦 VM EXISTENTE: Manjaro XFCE en GNOME Boxes"
echo "---------------------------------------------"
echo "✅ MÉTODO: Usar GNOME Boxes directamente"
echo "📍 Ubicación: ~/.var/app/org.gnome.Boxes/data/gnome-boxes/"
echo "🔧 Herramienta: Interfaz gráfica de GNOME Boxes"
echo ""

echo "🚀 VM NUEVA: Ubuntu-Dev en libvirt"
echo "-----------------------------------"
echo "✅ MÉTODO: Usar comandos virsh"
echo "📍 Ubicación: /home/<USER>/VMs/"
echo "🔧 Herramienta: Línea de comandos (virsh)"

echo ""
echo "📸 OPCIÓN 1: INSTANTÁNEA EN GNOME BOXES"
echo "======================================="

echo ""
echo "🎯 PARA TU VM MANJARO XFCE:"
echo ""
echo "1. Abrir GNOME Boxes"
echo "2. Seleccionar tu VM Manjaro XFCE"
echo "3. Clic derecho → 'Propiedades' o 'Configuración'"
echo "4. Buscar sección 'Instantáneas' o 'Snapshots'"
echo "5. Clic en 'Crear instantánea' o 'Nueva instantánea'"
echo "6. Dar nombre descriptivo (ej: 'Antes_de_optimizar')"
echo "7. Confirmar creación"
echo ""
echo "💡 NOTA: Si no aparece la opción, GNOME Boxes puede"
echo "    no soportar instantáneas en tu versión."

echo ""
echo "📸 OPCIÓN 2: INSTANTÁNEA CON LIBVIRT"
echo "==================================="

echo ""
echo "🎯 PARA TU VM UBUNTU-DEV:"

# Configurar libvirt
export LIBVIRT_DEFAULT_URI="qemu:///session"

echo ""
echo "🔍 Verificando estado de la VM..."
if virsh list --all | grep -q "Ubuntu-Dev"; then
    VM_STATUS=$(virsh list --all | grep "Ubuntu-Dev" | awk '{print $3}')
    echo "✅ VM Ubuntu-Dev encontrada - Estado: $VM_STATUS"
    
    echo ""
    echo "📸 CREAR INSTANTÁNEA:"
    echo "virsh snapshot-create-as Ubuntu-Dev 'snapshot-$(date +%Y%m%d-%H%M)' 'Instantánea antes de cambios'"
    echo ""
    echo "📋 VER INSTANTÁNEAS:"
    echo "virsh snapshot-list Ubuntu-Dev"
    echo ""
    echo "🔄 RESTAURAR INSTANTÁNEA:"
    echo "virsh snapshot-revert Ubuntu-Dev nombre-de-la-instantanea"
    echo ""
    echo "🗑️  ELIMINAR INSTANTÁNEA:"
    echo "virsh snapshot-delete Ubuntu-Dev nombre-de-la-instantanea"
    
else
    echo "❌ VM Ubuntu-Dev no encontrada"
fi

echo ""
echo "📸 OPCIÓN 3: RESPALDO MANUAL (SIEMPRE FUNCIONA)"
echo "==============================================="

echo ""
echo "🎯 PARA CUALQUIER VM:"
echo ""
echo "1. APAGAR LA VM COMPLETAMENTE"
echo "2. COPIAR EL ARCHIVO DE DISCO:"
echo ""
echo "   Para VM en GNOME Boxes:"
echo "   cp ~/.var/app/org.gnome.Boxes/data/gnome-boxes/images/manjaro-xfce-2-2 \\"
echo "      ~/.var/app/org.gnome.Boxes/data/gnome-boxes/images/manjaro-xfce-2-2.snapshot"
echo ""
echo "   Para VM en libvirt:"
echo "   cp /home/<USER>/VMs/Ubuntu-Dev.qcow2 \\"
echo "      /home/<USER>/VMs/Ubuntu-Dev.qcow2.snapshot"
echo ""
echo "3. ETIQUETAR CON FECHA:"
echo "   mv archivo.snapshot archivo.snapshot.$(date +%Y%m%d-%H%M)"

echo ""
echo "🚀 CREAR INSTANTÁNEAS AUTOMÁTICAMENTE"
echo "====================================="

echo ""
echo "📸 Script para instantánea de GNOME Boxes:"
cat << 'EOF'
#!/bin/bash
VM_FILE="$HOME/.var/app/org.gnome.Boxes/data/gnome-boxes/images/manjaro-xfce-2-2"
SNAPSHOT_NAME="manjaro-xfce-2-2.snapshot.$(date +%Y%m%d-%H%M)"
cp "$VM_FILE" "${VM_FILE%/*}/$SNAPSHOT_NAME"
echo "Instantánea creada: $SNAPSHOT_NAME"
EOF

echo ""
echo "📸 Script para instantánea de libvirt:"
cat << 'EOF'
#!/bin/bash
export LIBVIRT_DEFAULT_URI="qemu:///session"
SNAPSHOT_NAME="snapshot-$(date +%Y%m%d-%H%M)"
virsh snapshot-create-as Ubuntu-Dev "$SNAPSHOT_NAME" "Instantánea automática"
echo "Instantánea creada: $SNAPSHOT_NAME"
EOF

echo ""
echo "🎯 RECOMENDACIONES"
echo "=================="

echo ""
echo "✅ PARA TU VM MANJARO XFCE (GNOME Boxes):"
echo "   1. Intentar crear instantánea desde GNOME Boxes"
echo "   2. Si no funciona, usar respaldo manual"
echo "   3. Crear instantánea ANTES de cambiar recursos"
echo ""
echo "✅ PARA TU VM UBUNTU-DEV (libvirt):"
echo "   1. Usar comandos virsh (más eficiente)"
echo "   2. Las instantáneas son incrementales"
echo "   3. Puedes tener múltiples instantáneas"
echo ""
echo "💡 MEJORES PRÁCTICAS:"
echo "   • Crear instantánea antes de cambios importantes"
echo "   • Usar nombres descriptivos con fecha"
echo "   • No crear demasiadas (ocupan espacio)"
echo "   • Eliminar instantáneas antiguas regularmente"

echo ""
echo "🔧 COMANDOS ÚTILES PARA LIBVIRT"
echo "==============================="

echo ""
echo "# Crear instantánea"
echo "virsh snapshot-create-as Ubuntu-Dev 'nombre' 'descripción'"
echo ""
echo "# Listar instantáneas"
echo "virsh snapshot-list Ubuntu-Dev"
echo ""
echo "# Ver información de instantánea"
echo "virsh snapshot-info Ubuntu-Dev nombre-instantanea"
echo ""
echo "# Restaurar instantánea"
echo "virsh snapshot-revert Ubuntu-Dev nombre-instantanea"
echo ""
echo "# Eliminar instantánea"
echo "virsh snapshot-delete Ubuntu-Dev nombre-instantanea"
