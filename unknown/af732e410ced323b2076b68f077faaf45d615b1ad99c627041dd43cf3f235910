#!/bin/bash

# Script para crear instantánea de VM en GNOME Boxes

echo "📸 CREAR INSTANTÁNEA: VM MANJARO XFCE (GNOME Boxes)"
echo "=================================================="

VM_DIR="$HOME/.var/app/org.gnome.Boxes/data/gnome-boxes/images"
VM_FILE="manjaro-xfce-2-2"
VM_PATH="$VM_DIR/$VM_FILE"

echo ""
echo "🔍 VERIFICANDO VM..."
echo "==================="

if [ -f "$VM_PATH" ]; then
    echo "✅ VM encontrada: $VM_FILE"
    
    # Información de la VM
    VM_SIZE=$(du -h "$VM_PATH" | cut -f1)
    VM_DATE=$(stat -c %y "$VM_PATH" | cut -d' ' -f1)
    
    echo "   📊 Tamaño: $VM_SIZE"
    echo "   📅 Última modificación: $VM_DATE"
    echo "   📍 Ubicación: $VM_PATH"
else
    echo "❌ VM no encontrada en: $VM_PATH"
    echo ""
    echo "🔍 VMs disponibles en GNOME Boxes:"
    ls -la "$VM_DIR/" 2>/dev/null || echo "   Directorio no encontrado"
    exit 1
fi

echo ""
echo "⚠️  VERIFICANDO ESTADO DE LA VM..."
echo "=================================="

# Verificar si GNOME Boxes está ejecutándose
if pgrep -f "gnome-boxes" > /dev/null; then
    echo "🔄 GNOME Boxes está ejecutándose"
    echo ""
    echo "⚠️  IMPORTANTE: Para crear una instantánea segura:"
    echo "   1. Abre GNOME Boxes"
    echo "   2. APAGA completamente tu VM Manjaro XFCE"
    echo "   3. Espera a que aparezca como 'Apagada'"
    echo "   4. Luego ejecuta este script nuevamente"
    echo ""
    read -p "¿Tu VM está completamente apagada? (s/n): " VM_APAGADA
    
    if [[ ! "$VM_APAGADA" =~ ^[Ss]$ ]]; then
        echo ""
        echo "🔄 Por favor, apaga tu VM primero y ejecuta el script nuevamente."
        exit 1
    fi
else
    echo "✅ GNOME Boxes no está ejecutándose (perfecto para instantánea)"
fi

echo ""
echo "💾 VERIFICANDO ESPACIO DISPONIBLE..."
echo "===================================="

AVAILABLE_SPACE=$(df -h "$VM_DIR" | tail -1 | awk '{print $4}')
NEEDED_SPACE=$(du -h "$VM_PATH" | cut -f1)

echo "💽 Espacio disponible: $AVAILABLE_SPACE"
echo "📦 Espacio necesario: $NEEDED_SPACE"

# Verificar si hay suficiente espacio (simplificado)
AVAILABLE_GB=$(df "$VM_DIR" | tail -1 | awk '{print $4}')
NEEDED_GB=$(du "$VM_PATH" | cut -f1)

if [ "$AVAILABLE_GB" -lt "$NEEDED_GB" ]; then
    echo "❌ No hay suficiente espacio para crear la instantánea"
    echo "   Libera espacio o usa un disco externo"
    exit 1
else
    echo "✅ Espacio suficiente para crear instantánea"
fi

echo ""
echo "📸 CREANDO INSTANTÁNEA..."
echo "========================"

# Crear nombre de instantánea con fecha y hora
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
SNAPSHOT_NAME="${VM_FILE}.snapshot.${TIMESTAMP}"
SNAPSHOT_PATH="$VM_DIR/$SNAPSHOT_NAME"

echo "📝 Nombre de instantánea: $SNAPSHOT_NAME"
echo "📍 Ubicación: $SNAPSHOT_PATH"
echo ""
echo "⏳ Copiando archivo de VM..."
echo "   Esto puede tardar varios minutos (55GB)..."

# Crear instantánea con progreso si pv está disponible
if command -v pv &> /dev/null; then
    echo "📊 Copiando con indicador de progreso..."
    pv "$VM_PATH" > "$SNAPSHOT_PATH"
else
    echo "📊 Copiando (sin indicador de progreso)..."
    cp "$VM_PATH" "$SNAPSHOT_PATH"
fi

# Verificar que la copia fue exitosa
if [ $? -eq 0 ] && [ -f "$SNAPSHOT_PATH" ]; then
    echo ""
    echo "✅ INSTANTÁNEA CREADA EXITOSAMENTE"
    echo "================================="
    
    # Verificar tamaño de la instantánea
    SNAPSHOT_SIZE=$(du -h "$SNAPSHOT_PATH" | cut -f1)
    
    echo "✅ Instantánea: $SNAPSHOT_NAME"
    echo "✅ Tamaño: $SNAPSHOT_SIZE"
    echo "✅ Ubicación: $SNAPSHOT_PATH"
    
    # Crear archivo de información
    INFO_FILE="$VM_DIR/${SNAPSHOT_NAME}.info"
    cat > "$INFO_FILE" << EOF
INSTANTÁNEA DE VM MANJARO XFCE
==============================
Fecha de creación: $(date)
VM original: $VM_PATH
Instantánea: $SNAPSHOT_PATH
Tamaño original: $VM_SIZE
Tamaño instantánea: $SNAPSHOT_SIZE
Sistema: SuperManjaro optimizado

PARA RESTAURAR ESTA INSTANTÁNEA:
1. Apagar completamente la VM en GNOME Boxes
2. Ejecutar: cp "$SNAPSHOT_PATH" "$VM_PATH"
3. Reiniciar GNOME Boxes

NOTA: Esto sobrescribirá el estado actual de la VM
EOF
    
    echo "📄 Archivo de información: ${SNAPSHOT_NAME}.info"
    
else
    echo ""
    echo "❌ ERROR AL CREAR INSTANTÁNEA"
    echo "============================="
    echo "La instantánea no se pudo crear correctamente."
    echo "Verifica el espacio disponible y permisos."
    
    # Limpiar archivo parcial si existe
    [ -f "$SNAPSHOT_PATH" ] && rm -f "$SNAPSHOT_PATH"
    exit 1
fi

echo ""
echo "🎯 PRÓXIMOS PASOS"
echo "================="
echo ""
echo "✅ INSTANTÁNEA LISTA PARA USAR:"
echo "   • Ahora puedes modificar tu VM con seguridad"
echo "   • Si algo sale mal, puedes restaurar fácilmente"
echo ""
echo "🔄 PARA RESTAURAR (si es necesario):"
echo "   1. Apagar VM en GNOME Boxes"
echo "   2. Ejecutar: cp \"$SNAPSHOT_PATH\" \"$VM_PATH\""
echo "   3. Reiniciar GNOME Boxes"
echo ""
echo "🚀 AHORA PUEDES:"
echo "   • Aumentar RAM y CPUs de tu VM"
echo "   • Instalar software experimental"
echo "   • Hacer cambios de configuración"
echo ""
echo "📁 GESTIÓN DE INSTANTÁNEAS:"
echo "   • Ver todas: ls -la \"$VM_DIR/\"*.snapshot.*"
echo "   • Eliminar antigua: rm \"$SNAPSHOT_PATH\""
echo ""
echo "💡 TIP: Crea instantáneas antes de cambios importantes"
