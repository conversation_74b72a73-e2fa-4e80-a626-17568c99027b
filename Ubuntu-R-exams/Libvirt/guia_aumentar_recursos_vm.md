# 🚀 GUÍA: AUME<PERSON><PERSON> RECURSOS DE TU VM EXISTENTE

## 📊 ESTADO ACTUAL DETECTADO
- **VM:** Manjaro XFCE (55GB)
- **Sistema anfitrión:** 40GB RAM optimizados + 12 CPUs
- **Ubicación:** GNOME Boxes Flatpak

---

## 🎯 OBJETIVO
Aumentar de **4-8GB RAM** → **12-16GB RAM**
Aumentar de **2-4 CPUs** → **6-8 CPUs**

---

## 📋 PASO A PASO

### 1️⃣ **PREPARAR LA VM**

#### ⚠️ **IMPORTANTE: Ha<PERSON> respaldo**
```bash
# Crear respaldo de la VM (opcional pero recomendado)
cp ~/.var/app/org.gnome.Boxes/data/gnome-boxes/images/manjaro-xfce-2-2 \
   ~/.var/app/org.gnome.Boxes/data/gnome-boxes/images/manjaro-xfce-2-2.backup
```

#### 🔄 **Apagar la VM**
1. Abrir **GNOME Boxes**
2. Si tu VM está ejecutándose, **apagarla completamente**
3. Esperar a que aparezca como "Apagada"

---

### 2️⃣ **AUMENTAR RECURSOS**

#### 🖥️ **Acceder a Propiedades**
1. **Clic derecho** en tu VM Manjaro XFCE
2. Seleccionar **"Propiedades"** o **"Preferencias"**
3. Buscar la sección **"Sistema"** o **"Hardware"**

#### 🧠 **Configurar RAM**
```
CONFIGURACIÓN RECOMENDADA:
┌─────────────────────────────┐
│ RAM: 12 GB (12288 MB)       │
│                             │
│ Antes: 4-8 GB               │
│ Ahora:  12 GB               │
│ Disponible: 40 GB total     │
└─────────────────────────────┘
```

**Pasos:**
1. Buscar **"Memoria"** o **"RAM"**
2. Cambiar a **12288 MB** (12 GB)
3. Si quieres más rendimiento: **16384 MB** (16 GB)

#### ⚡ **Configurar CPUs**
```
CONFIGURACIÓN RECOMENDADA:
┌─────────────────────────────┐
│ CPUs: 6 núcleos             │
│                             │
│ Antes: 2-4 núcleos          │
│ Ahora:  6 núcleos           │
│ Disponible: 12 núcleos      │
└─────────────────────────────┘
```

**Pasos:**
1. Buscar **"Procesadores"** o **"CPUs"**
2. Cambiar a **6 núcleos**
3. Para máximo rendimiento: **8 núcleos**

#### 💾 **Verificar Almacenamiento**
- **Actual:** 55 GB (suficiente)
- **Disponible:** 166 GB en el host
- **Acción:** Mantener como está (55 GB es adecuado)

---

### 3️⃣ **CONFIGURACIONES ADICIONALES**

#### 🎮 **Gráficos y Display**
- **Aceleración 3D:** ✅ Habilitar si está disponible
- **Resolución:** Configurar según tu monitor
- **Memoria de video:** Máximo disponible

#### 🌐 **Red**
- **Tipo:** NAT (recomendado)
- **Compartir red del host:** ✅ Habilitado

#### 🔧 **Opciones Avanzadas**
- **Virtualización anidada:** ✅ Si está disponible
- **UEFI:** Mantener configuración actual
- **Secure Boot:** Según necesidades

---

### 4️⃣ **GUARDAR Y INICIAR**

1. **Aplicar cambios** (botón "Aplicar" o "Guardar")
2. **Cerrar** ventana de propiedades
3. **Iniciar la VM** (doble clic)
4. **Esperar** 1-2 minutos para el arranque

---

## 🔍 VERIFICACIÓN DENTRO DE LA VM

### 📊 **Comprobar RAM**
```bash
# Dentro de tu VM Manjaro
free -h
```
**Resultado esperado:** ~12 GB total

### ⚡ **Comprobar CPUs**
```bash
# Dentro de tu VM Manjaro
nproc
lscpu | grep "CPU(s):"
```
**Resultado esperado:** 6 CPUs

### 🎯 **Benchmark rápido**
```bash
# Test de CPU (dentro de la VM)
time dd if=/dev/zero of=/tmp/test bs=1M count=1000

# Test de RAM
free -h && sync && echo 3 > /proc/sys/vm/drop_caches && free -h
```

---

## 🚀 OPTIMIZACIONES ADICIONALES

### 🔧 **Dentro de la VM Manjaro**
```bash
# Actualizar sistema
sudo pacman -Syu

# Instalar herramientas de integración
sudo pacman -S spice-vdagent

# Optimizar swappiness
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf

# Reiniciar para aplicar cambios
sudo reboot
```

### 🎨 **Optimizar XFCE**
1. **Panel de control** → **Configuración**
2. **Gestor de ventanas** → **Compositor**
3. **Deshabilitar efectos** innecesarios
4. **Configurar** tema ligero

---

## 📈 RESULTADOS ESPERADOS

### ⚡ **Mejoras de Rendimiento**
- **Arranque:** 30-50% más rápido
- **Aplicaciones:** 40-60% más responsivas
- **Multitarea:** 70-80% mejor
- **Compilación:** 2-3x más rápida

### 🎯 **Comparación**
```
ANTES (4GB RAM, 2 CPUs):
├── Firefox: Lento con múltiples pestañas
├── Compilación: 10-15 minutos
└── Multitarea: Limitada

DESPUÉS (12GB RAM, 6 CPUs):
├── Firefox: Fluido con 20+ pestañas
├── Compilación: 3-5 minutos
└── Multitarea: Excelente
```

---

## 🆘 SOLUCIÓN DE PROBLEMAS

### ❌ **VM no inicia**
1. Reducir RAM a 8GB temporalmente
2. Verificar que el host tiene suficiente RAM libre
3. Reiniciar GNOME Boxes

### ❌ **Rendimiento no mejora**
1. Verificar que los cambios se aplicaron: `free -h` y `nproc`
2. Instalar spice-vdagent
3. Reiniciar la VM

### ❌ **Sistema anfitrión lento**
1. Reducir recursos de la VM
2. Cerrar aplicaciones innecesarias del host
3. Verificar uso de RAM: `htop`

---

## 🎊 ¡DISFRUTA TU VM OPTIMIZADA!

Tu VM Manjaro XFCE ahora tiene:
- ✅ **3x más RAM** (12GB vs 4GB)
- ✅ **3x más CPUs** (6 vs 2)
- ✅ **Optimizaciones de SuperManjaro**
- ✅ **Rendimiento profesional**

¡Perfecto para desarrollo, compilación y multitarea! 🖥️✨
