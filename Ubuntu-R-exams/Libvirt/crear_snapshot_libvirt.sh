#!/bin/bash

# Script para crear instantánea de VM en libvirt

echo "📸 CREAR INSTANTÁNEA: VM UBUNTU-DEV (libvirt)"
echo "============================================="

VM_NAME="Ubuntu-Dev"

# Configurar libvirt de usuario
export LIBVIRT_DEFAULT_URI="qemu:///session"

echo ""
echo "🔍 VERIFICANDO VM..."
echo "==================="

# Verificar que la VM existe
if virsh list --all | grep -q "$VM_NAME"; then
    VM_STATUS=$(virsh list --all | grep "$VM_NAME" | awk '{print $3}')
    echo "✅ VM encontrada: $VM_NAME"
    echo "📊 Estado actual: $VM_STATUS"
    
    # Mostrar información de la VM
    echo ""
    echo "📋 Información de la VM:"
    virsh dominfo "$VM_NAME" | grep -E "(Max memory|Used memory|CPU|State)"
    
else
    echo "❌ VM '$VM_NAME' no encontrada"
    echo ""
    echo "🔍 VMs disponibles:"
    virsh list --all
    exit 1
fi

echo ""
echo "📸 VERIFICANDO INSTANTÁNEAS EXISTENTES..."
echo "========================================"

# Listar instantáneas existentes
EXISTING_SNAPSHOTS=$(virsh snapshot-list "$VM_NAME" --name 2>/dev/null)

if [ -n "$EXISTING_SNAPSHOTS" ]; then
    echo "📋 Instantáneas existentes:"
    virsh snapshot-list "$VM_NAME" --tree 2>/dev/null || virsh snapshot-list "$VM_NAME"
else
    echo "📭 No hay instantáneas existentes (esta será la primera)"
fi

echo ""
echo "🎯 CONFIGURANDO NUEVA INSTANTÁNEA..."
echo "==================================="

# Crear nombre de instantánea con fecha y hora
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
SNAPSHOT_NAME="snapshot_${TIMESTAMP}"
DESCRIPTION="Instantánea creada el $(date '+%Y-%m-%d %H:%M:%S') - SuperManjaro"

echo "📝 Nombre: $SNAPSHOT_NAME"
echo "📄 Descripción: $DESCRIPTION"

# Preguntar si la VM debe estar ejecutándose o apagada
echo ""
echo "⚙️  OPCIONES DE INSTANTÁNEA:"
echo "============================"
echo ""
echo "1. 📸 INSTANTÁNEA EN VIVO (VM ejecutándose)"
echo "   • Captura el estado actual de RAM y disco"
echo "   • Más completa pero tarda más"
echo "   • Recomendada si la VM está trabajando"
echo ""
echo "2. 📸 INSTANTÁNEA DE DISCO (VM apagada)"
echo "   • Solo captura el estado del disco"
echo "   • Más rápida y eficiente"
echo "   • Recomendada para cambios de configuración"
echo ""

if [ "$VM_STATUS" = "ejecutando" ]; then
    echo "🔄 Tu VM está EJECUTÁNDOSE"
    echo "   Recomendación: Instantánea en vivo"
    SNAPSHOT_TYPE="live"
    EXTRA_FLAGS="--live"
elif [ "$VM_STATUS" = "apagado" ]; then
    echo "⏹️  Tu VM está APAGADA"
    echo "   Recomendación: Instantánea de disco"
    SNAPSHOT_TYPE="offline"
    EXTRA_FLAGS=""
else
    echo "⚠️  Estado de VM: $VM_STATUS"
    SNAPSHOT_TYPE="auto"
    EXTRA_FLAGS=""
fi

echo ""
echo "📸 CREANDO INSTANTÁNEA..."
echo "========================"

echo "⏳ Ejecutando comando de instantánea..."
echo "   Comando: virsh snapshot-create-as \"$VM_NAME\" \"$SNAPSHOT_NAME\" \"$DESCRIPTION\" $EXTRA_FLAGS"

# Crear la instantánea
if virsh snapshot-create-as "$VM_NAME" "$SNAPSHOT_NAME" "$DESCRIPTION" $EXTRA_FLAGS; then
    echo ""
    echo "✅ INSTANTÁNEA CREADA EXITOSAMENTE"
    echo "================================="
    
    # Mostrar información de la instantánea creada
    echo "✅ Nombre: $SNAPSHOT_NAME"
    echo "✅ Descripción: $DESCRIPTION"
    echo "✅ Tipo: $SNAPSHOT_TYPE"
    echo "✅ VM: $VM_NAME"
    
    # Mostrar información detallada
    echo ""
    echo "📋 Información detallada:"
    virsh snapshot-info "$VM_NAME" "$SNAPSHOT_NAME"
    
else
    echo ""
    echo "❌ ERROR AL CREAR INSTANTÁNEA"
    echo "============================="
    echo "La instantánea no se pudo crear."
    echo ""
    echo "🔧 Posibles soluciones:"
    echo "   • Verificar que la VM existe: virsh list --all"
    echo "   • Verificar permisos de libvirt"
    echo "   • Verificar espacio en disco"
    echo "   • Si la VM está ejecutándose, intentar apagarla primero"
    exit 1
fi

echo ""
echo "📋 TODAS LAS INSTANTÁNEAS ACTUALES:"
echo "=================================="
virsh snapshot-list "$VM_NAME" --tree 2>/dev/null || virsh snapshot-list "$VM_NAME"

echo ""
echo "🎯 COMANDOS ÚTILES"
echo "=================="
echo ""
echo "📋 VER TODAS LAS INSTANTÁNEAS:"
echo "virsh snapshot-list $VM_NAME"
echo ""
echo "📄 VER INFORMACIÓN DE ESTA INSTANTÁNEA:"
echo "virsh snapshot-info $VM_NAME $SNAPSHOT_NAME"
echo ""
echo "🔄 RESTAURAR ESTA INSTANTÁNEA:"
echo "virsh snapshot-revert $VM_NAME $SNAPSHOT_NAME"
echo ""
echo "🗑️  ELIMINAR ESTA INSTANTÁNEA:"
echo "virsh snapshot-delete $VM_NAME $SNAPSHOT_NAME"
echo ""
echo "📸 CREAR OTRA INSTANTÁNEA:"
echo "./crear_snapshot_libvirt.sh"

echo ""
echo "🎊 INSTANTÁNEA LISTA"
echo "==================="
echo ""
echo "✅ Tu VM $VM_NAME ahora tiene una instantánea de seguridad"
echo "✅ Puedes hacer cambios con confianza"
echo "✅ Si algo sale mal, puedes restaurar fácilmente"
echo ""
echo "🚀 AHORA PUEDES:"
echo "   • Modificar configuración de la VM"
echo "   • Instalar software experimental"
echo "   • Hacer cambios del sistema"
echo "   • Probar configuraciones nuevas"
echo ""
echo "💡 TIP: Crea instantáneas antes de cada cambio importante"
